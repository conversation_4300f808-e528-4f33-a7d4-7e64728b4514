import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Play, Target, BookOpen } from 'lucide-react-native';
import { BottomSheetModal } from '@/components/ui/BottomSheetModal';
import { Button } from '@/components/ui/Button';
import { useTheme } from '@/contexts/ThemeContext';
import { TaskType } from '@/types/app';

interface TimerStartModalProps {
  visible: boolean;
  onClose: () => void;
  onStartTimer: (taskName: string, taskType: TaskType) => void;
  initialTaskName?: string;
  initialTaskType?: TaskType;
}

export const TimerStartModal: React.FC<TimerStartModalProps> = ({
  visible,
  onClose,
  onStartTimer,
  initialTaskName = '',
  initialTaskType = 'General Study',
}) => {
  const { theme } = useTheme();
  const [taskName, setTaskName] = useState(initialTaskName);
  const [taskType, setTaskType] = useState<TaskType>(initialTaskType);

  const taskTypes: { value: TaskType; label: string; icon: string; color: string }[] = [
    { value: 'Lecture', label: 'Lecture', icon: 'school', color: '#BB86FC' },
    { value: 'Exercise', label: 'Exercise', icon: 'fitness-center', color: '#2196F3' },
    { value: 'Reading', label: 'Reading', icon: 'menu-book', color: '#00BCD4' },
    { value: 'Practice', label: 'Practice', icon: 'psychology', color: '#4CAF50' },
    { value: 'Review', label: 'Review', icon: 'rate-review', color: '#FF9800' },
    { value: 'General Study', label: 'General Study', icon: 'book', color: '#9C27B0' },
    { value: 'Custom', label: 'Custom', icon: 'edit', color: '#607D8B' },
  ];

  const handleStart = () => {
    if (!taskName.trim()) {
      Alert.alert('Task Name Required', 'Please enter a task name or description.');
      return;
    }
    onStartTimer(taskName, taskType);
    onClose();
  };

  const resetForm = () => {
    setTaskName(initialTaskName);
    setTaskType(initialTaskType);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <BottomSheetModal
      visible={visible}
      onClose={handleClose}
      title="Start Focus Session"
      height="auto"
      scrollable={true}
    >
      {/* Header Section */}
      <View style={styles.headerSection}>
        <LinearGradient
          colors={[theme.colors.accent.primary + '20', theme.colors.background.modal]}
          style={styles.headerGradient}
        >
          <View style={styles.iconContainer}>
            <LinearGradient
              colors={[theme.colors.accent.primary, theme.colors.accent.secondary]}
              style={styles.playIcon}
            >
              <Play size={24} color="#FFFFFF" fill="#FFFFFF" />
            </LinearGradient>
          </View>
          <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
            Set up your focus session with a clear goal
          </Text>
        </LinearGradient>
      </View>

      {/* Task Name Input */}
      <View style={styles.section}>
        <View style={styles.inputHeader}>
          <Target size={20} color={theme.colors.accent.primary} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
            What will you focus on?
          </Text>
        </View>
        <TextInput
          style={[
            styles.taskInput,
            {
              backgroundColor: theme.colors.background.tertiary,
              borderColor: theme.colors.ui.border,
              color: theme.colors.text.primary,
            }
          ]}
          placeholder="Enter your task or goal..."
          placeholderTextColor={theme.colors.text.tertiary}
          value={taskName}
          onChangeText={setTaskName}
          multiline
          numberOfLines={3}
          textAlignVertical="top"
          maxLength={200}
        />
        <Text style={[styles.charCount, { color: theme.colors.text.tertiary }]}>
          {taskName.length}/200
        </Text>
      </View>

      {/* Task Type Selection */}
      <View style={styles.section}>
        <View style={styles.inputHeader}>
          <BookOpen size={20} color={theme.colors.accent.primary} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
            Study Type
          </Text>
        </View>
        <View style={styles.taskTypeGrid}>
          {taskTypes.map((type) => (
            <TouchableOpacity
              key={type.value}
              style={[
                styles.taskTypeCard,
                {
                  backgroundColor: taskType === type.value 
                    ? type.color + '20' 
                    : theme.colors.background.tertiary,
                  borderColor: taskType === type.value 
                    ? type.color 
                    : theme.colors.ui.border,
                }
              ]}
              onPress={() => setTaskType(type.value)}
              activeOpacity={0.7}
            >
              <MaterialIcons
                name={type.icon as any}
                size={24}
                color={taskType === type.value ? type.color : theme.colors.text.secondary}
              />
              <Text
                style={[
                  styles.taskTypeLabel,
                  {
                    color: taskType === type.value 
                      ? type.color 
                      : theme.colors.text.secondary,
                    fontWeight: taskType === type.value ? '600' : '500',
                  }
                ]}
              >
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionSection}>
        <Button
          title="Start Session"
          onPress={handleStart}
          variant="primary"
          size="lg"
          style={[
            styles.startButton,
            { backgroundColor: theme.colors.accent.primary }
          ]}
          textStyle={{ color: theme.colors.text.inverse, fontWeight: '600' }}
          icon={<Play size={20} color="#FFFFFF" fill="#FFFFFF" />}
          iconPosition="left"
        />
        
        <Button
          title="Cancel"
          onPress={handleClose}
          variant="outline"
          size="lg"
          style={[
            styles.cancelButton,
            {
              borderColor: theme.colors.ui.border,
              backgroundColor: 'transparent',
            }
          ]}
          textStyle={{ color: theme.colors.text.secondary }}
        />
      </View>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  // Removed container and content styles as they are no longer needed
  headerSection: {
    marginBottom: 32,
  },
  headerGradient: {
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 12,
  },
  playIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  section: {
    marginBottom: 28,
  },
  inputHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  taskInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 80,
    lineHeight: 22,
  },
  charCount: {
    fontSize: 12,
    textAlign: 'right',
    marginTop: 4,
  },
  taskTypeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  taskTypeCard: {
    flex: 1,
    minWidth: '45%',
    maxWidth: '48%',
    borderWidth: 2,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
  },
  taskTypeLabel: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  actionSection: {
    gap: 12,
    marginTop: 8,
  },
  startButton: {
    borderRadius: 16,
    paddingVertical: 16,
  },
  cancelButton: {
    borderRadius: 16,
    paddingVertical: 16,
    borderWidth: 1,
  },
});
