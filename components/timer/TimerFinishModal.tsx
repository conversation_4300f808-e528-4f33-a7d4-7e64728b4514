import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { CheckCircle, Star, MessageSquare, Save, SkipForward } from 'lucide-react-native';
import { BottomSheetModal } from '@/components/ui/BottomSheetModal';
import { Button } from '@/components/ui/Button';
import { useTheme } from '@/contexts/ThemeContext';
import { TimerSession, SessionSummary, TaskType } from '@/types/app';

interface TimerFinishModalProps {
  visible: boolean;
  onClose: () => void;
  onCompleteSession: (summary: SessionSummary) => void;
  session: TimerSession;
  completionType: 'complete' | 'cancel';
}

export const TimerFinishModal: React.FC<TimerFinishModalProps> = ({
  visible,
  onClose,
  onCompleteSession,
  session,
  completionType,
}) => {
  const { theme } = useTheme();
  const [productivityRating, setProductivityRating] = useState<number>(0);
  const [notes, setNotes] = useState('');
  const [feedback, setFeedback] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    }
    return `${secs}s`;
  };

  const getCompletionTitle = (): string => {
    return completionType === 'complete' ? 'Session Complete! 🎉' : 'Session Cancelled';
  };

  const getCompletionMessage = (): string => {
    return completionType === 'complete' 
      ? 'Great work! How did your session go?' 
      : 'Your session has been cancelled.';
  };

  const handleComplete = async () => {
    setIsLoading(true);
    try {
      const summary: SessionSummary = {
        duration: session.duration,
        subject: session.subject || '',
        taskName: session.taskName || '',
        taskType: session.taskType || 'General Study',
        mode: session.mode,
        phase: session.phase || 'work',
        completed: completionType === 'complete',
        productivityRating: completionType === 'complete' ? productivityRating : undefined,
        notes: notes.trim() || undefined,
        feedback: feedback.trim() || undefined,
      };
      
      await onCompleteSession(summary);
      resetForm();
      onClose();
    } catch (error) {
      console.error('Error completing session:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = async () => {
    setIsLoading(true);
    try {
      const summary: SessionSummary = {
        duration: session.duration,
        subject: session.subject || '',
        taskName: session.taskName || '',
        taskType: session.taskType || 'General Study',
        mode: session.mode,
        phase: session.phase || 'work',
        completed: completionType === 'complete',
      };
      
      await onCompleteSession(summary);
      resetForm();
      onClose();
    } catch (error) {
      console.error('Error completing session:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setProductivityRating(0);
    setNotes('');
    setFeedback('');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const renderRatingStars = () => {
    return (
      <View style={styles.ratingContainer}>
        {[1, 2, 3, 4, 5].map((rating) => (
          <TouchableOpacity
            key={rating}
            onPress={() => setProductivityRating(rating)}
            style={styles.starButton}
            activeOpacity={0.7}
          >
            <Star
              size={32}
              color={rating <= productivityRating ? '#FFD700' : theme.colors.text.tertiary}
              fill={rating <= productivityRating ? '#FFD700' : 'transparent'}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <BottomSheetModal
      visible={visible}
      onClose={handleClose}
      title={getCompletionTitle()}
      height="auto"
      scrollable={true}
    >
      {/* Header Section */}
      <View style={styles.headerSection}>
        <LinearGradient
          colors={completionType === 'complete' 
            ? ['#10B98120', theme.colors.background.modal]
            : ['#EF444420', theme.colors.background.modal]
          }
          style={styles.headerGradient}
        >
          <View style={styles.iconContainer}>
            <LinearGradient
              colors={completionType === 'complete' 
                ? ['#10B981', '#059669']
                : ['#EF4444', '#DC2626']
              }
              style={styles.completionIcon}
            >
              {completionType === 'complete' ? (
                <CheckCircle size={28} color="#FFFFFF" />
              ) : (
                <MaterialIcons name="cancel" size={28} color="#FFFFFF" />
              )}
            </LinearGradient>
          </View>
          <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
            {getCompletionMessage()}
          </Text>
        </LinearGradient>
      </View>

      {/* Session Summary */}
      <View style={styles.summarySection}>
        <View style={[
          styles.summaryCard,
          {
            backgroundColor: theme.colors.background.tertiary,
            borderColor: theme.colors.ui.border,
          }
        ]}>
          <Text style={[styles.summaryTitle, { color: theme.colors.text.primary }]}>
            Session Summary
          </Text>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.text.secondary }]}>
              Duration:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.text.primary }]}>
              {formatTime(session.duration)}
            </Text>
          </View>
          
          {session.taskName && (
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: theme.colors.text.secondary }]}>
                Task:
              </Text>
              <Text style={[styles.summaryValue, { color: theme.colors.text.primary }]}>
                {session.taskName}
              </Text>
            </View>
          )}
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.text.secondary }]}>
              Type:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.text.primary }]}>
              {session.taskType || 'General Study'}
            </Text>
          </View>
        </View>
      </View>

      {/* Feedback Section - Only for completed sessions */}
      {completionType === 'complete' && (
        <>
          {/* Productivity Rating */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              How productive was this session?
            </Text>
            {renderRatingStars()}
            <Text style={[styles.ratingLabel, { color: theme.colors.text.tertiary }]}>
              {productivityRating === 0 ? 'Tap to rate' : 
               productivityRating === 1 ? 'Not very productive' :
               productivityRating === 2 ? 'Somewhat productive' :
               productivityRating === 3 ? 'Moderately productive' :
               productivityRating === 4 ? 'Very productive' :
               'Extremely productive'}
            </Text>
          </View>

          {/* Notes */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              Session Notes (Optional)
            </Text>
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: theme.colors.background.tertiary,
                  borderColor: theme.colors.ui.border,
                  color: theme.colors.text.primary,
                }
              ]}
              placeholder="What did you accomplish?"
              placeholderTextColor={theme.colors.text.tertiary}
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={2}
              textAlignVertical="top"
              maxLength={200}
            />
          </View>

          {/* Feedback */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              Additional Feedback (Optional)
            </Text>
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: theme.colors.background.tertiary,
                  borderColor: theme.colors.ui.border,
                  color: theme.colors.text.primary,
                }
              ]}
              placeholder="Any challenges or insights?"
              placeholderTextColor={theme.colors.text.tertiary}
              value={feedback}
              onChangeText={setFeedback}
              multiline
              numberOfLines={2}
              textAlignVertical="top"
              maxLength={200}
            />
          </View>
        </>
      )}

      {/* Action Buttons */}
      <View style={styles.actionSection}>
        {completionType === 'complete' && (
          <Button
            title="Save Session"
            onPress={handleComplete}
            variant="primary"
            size="lg"
            style={[
              styles.saveButton,
              { backgroundColor: theme.colors.accent.primary }
            ]}
            textStyle={{ color: theme.colors.text.inverse, fontWeight: '600' }}
            icon={<Save size={20} color="#FFFFFF" />}
            iconPosition="left"
            loading={isLoading}
            disabled={isLoading}
          />
        )}
        
        <Button
          title={completionType === 'complete' ? "Skip Feedback" : "Close"}
          onPress={handleSkip}
          variant="outline"
          size="lg"
          style={[
            styles.skipButton,
            {
              borderColor: theme.colors.ui.border,
              backgroundColor: 'transparent',
            }
          ]}
          textStyle={{ color: theme.colors.text.secondary }}
          icon={completionType === 'complete' ? 
            <SkipForward size={20} color={theme.colors.text.secondary} /> : 
            undefined
          }
          iconPosition="left"
          loading={isLoading}
          disabled={isLoading}
        />
      </View>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  // Removed container and content styles as they are no longer needed
  headerSection: {
    marginBottom: 24,
  },
  headerGradient: {
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 12,
  },
  completionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  summarySection: {
    marginBottom: 24,
  },
  summaryCard: {
    borderWidth: 1,
    borderRadius: 16,
    padding: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
    marginBottom: 8,
  },
  starButton: {
    padding: 4,
  },
  ratingLabel: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 60,
    lineHeight: 22,
  },
  actionSection: {
    gap: 12,
    marginTop: 8,
  },
  saveButton: {
    borderRadius: 16,
    paddingVertical: 16,
  },
  skipButton: {
    borderRadius: 16,
    paddingVertical: 16,
    borderWidth: 1,
  },
});
