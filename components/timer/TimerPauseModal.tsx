import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Pause, Clock, BookOpen, Save } from 'lucide-react-native';
import { BottomSheetModal } from '@/components/ui/BottomSheetModal';
import { Button } from '@/components/ui/Button';
import { useTheme } from '@/contexts/ThemeContext';
import { TimerSession, TaskType } from '@/types/app';

interface TimerPauseModalProps {
  visible: boolean;
  onClose: () => void;
  onPauseSession: (notes?: string) => void;
  onContinueSession: () => void;
  session: TimerSession;
  currentTime: number;
}

export const TimerPauseModal: React.FC<TimerPauseModalProps> = ({
  visible,
  onClose,
  onPauseSession,
  onContinueSession,
  session,
  currentTime,
}) => {
  const { theme } = useTheme();
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    }
    return `${secs}s`;
  };

  const getTaskTypeIcon = (taskType?: TaskType): string => {
    switch (taskType) {
      case 'Lecture': return 'school';
      case 'Exercise': return 'fitness-center';
      case 'Reading': return 'menu-book';
      case 'Practice': return 'psychology';
      case 'Review': return 'rate-review';
      case 'Custom': return 'edit';
      default: return 'book';
    }
  };

  const getTaskTypeColor = (taskType?: TaskType): string => {
    switch (taskType) {
      case 'Lecture': return '#BB86FC';
      case 'Exercise': return '#2196F3';
      case 'Reading': return '#00BCD4';
      case 'Practice': return '#4CAF50';
      case 'Review': return '#FF9800';
      case 'Custom': return '#607D8B';
      default: return '#9C27B0';
    }
  };

  const handlePause = async () => {
    setIsLoading(true);
    try {
      await onPauseSession(notes.trim() || undefined);
      setNotes('');
      onClose();
    } catch (error) {
      console.error('Error pausing session:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleContinue = () => {
    setNotes('');
    onContinueSession();
    onClose();
  };

  const handleClose = () => {
    setNotes('');
    onClose();
  };

  // Debug logging
  console.log('TimerPauseModal render:', {
    visible,
    session,
    currentTime,
    themeColors: theme.colors.background
  });

  return (
    <BottomSheetModal
      visible={visible}
      onClose={handleClose}
      title="Pause Session"
      height={600}
      scrollable={true}
    >
      {/* Simple Test Content */}
      <View style={{ padding: 20, backgroundColor: 'red', minHeight: 200 }}>
        <Text style={{ color: 'white', fontSize: 24, fontWeight: 'bold' }}>
          TEST MODAL CONTENT
        </Text>
        <Text style={{ color: 'white', fontSize: 16, marginTop: 10 }}>
          Session: {session?.id || 'No session'}
        </Text>
        <Text style={{ color: 'white', fontSize: 16, marginTop: 5 }}>
          Time: {formatTime(currentTime)}
        </Text>
        <Text style={{ color: 'white', fontSize: 16, marginTop: 5 }}>
          Subject: {session?.subject || 'No subject'}
        </Text>

        <TouchableOpacity
          style={{
            backgroundColor: 'blue',
            padding: 15,
            borderRadius: 8,
            marginTop: 20,
            alignItems: 'center'
          }}
          onPress={handlePause}
        >
          <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>
            PAUSE SESSION
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            backgroundColor: 'green',
            padding: 15,
            borderRadius: 8,
            marginTop: 10,
            alignItems: 'center'
          }}
          onPress={handleContinue}
        >
          <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>
            CONTINUE SESSION
          </Text>
        </TouchableOpacity>
      </View>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  // Removed container style as it is no longer needed
  headerSection: {
    marginBottom: 24,
  },
  headerGradient: {
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 12,
  },
  pauseIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  summarySection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  summaryCard: {
    borderWidth: 1,
    borderRadius: 16,
    padding: 20,
  },
  timeDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  timeText: {
    fontSize: 24,
    fontWeight: '700',
    marginLeft: 8,
  },
  taskInfo: {
    gap: 8,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taskType: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  taskName: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 22,
  },
  subjectContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subjectText: {
    fontSize: 14,
    marginLeft: 6,
  },
  notesSection: {
    marginBottom: 24,
  },
  notesInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 80,
    lineHeight: 22,
  },
  charCount: {
    fontSize: 12,
    textAlign: 'right',
    marginTop: 4,
  },
  actionSection: {
    gap: 12,
  },
  pauseButton: {
    borderRadius: 16,
    paddingVertical: 16,
  },
  continueButton: {
    borderRadius: 16,
    paddingVertical: 16,
    borderWidth: 2,
  },
});
