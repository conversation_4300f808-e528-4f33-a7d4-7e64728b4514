import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Pause, Clock, BookOpen, Save } from 'lucide-react-native';
import { BottomSheetModal } from '@/components/ui/BottomSheetModal';
import { Button } from '@/components/ui/Button';
import { useTheme } from '@/contexts/ThemeContext';
import { TimerSession, TaskType } from '@/types/app';

interface TimerPauseModalProps {
  visible: boolean;
  onClose: () => void;
  onPauseSession: (notes?: string) => void;
  onContinueSession: () => void;
  session: TimerSession;
  currentTime: number;
}

export const TimerPauseModal: React.FC<TimerPauseModalProps> = ({
  visible,
  onClose,
  onPauseSession,
  onContinueSession,
  session,
  currentTime,
}) => {
  const { theme } = useTheme();
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    }
    return `${secs}s`;
  };

  const getTaskTypeIcon = (taskType?: TaskType): string => {
    switch (taskType) {
      case 'Lecture': return 'school';
      case 'Exercise': return 'fitness-center';
      case 'Reading': return 'menu-book';
      case 'Practice': return 'psychology';
      case 'Review': return 'rate-review';
      case 'Custom': return 'edit';
      default: return 'book';
    }
  };

  const getTaskTypeColor = (taskType?: TaskType): string => {
    switch (taskType) {
      case 'Lecture': return '#BB86FC';
      case 'Exercise': return '#2196F3';
      case 'Reading': return '#00BCD4';
      case 'Practice': return '#4CAF50';
      case 'Review': return '#FF9800';
      case 'Custom': return '#607D8B';
      default: return '#9C27B0';
    }
  };

  const handlePause = async () => {
    setIsLoading(true);
    try {
      await onPauseSession(notes.trim() || undefined);
      setNotes('');
      onClose();
    } catch (error) {
      console.error('Error pausing session:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleContinue = () => {
    setNotes('');
    onContinueSession();
    onClose();
  };

  const handleClose = () => {
    setNotes('');
    onClose();
  };

  return (
    <BottomSheetModal
      visible={visible}
      onClose={handleClose}
      title="Pause Session"
      height="auto"
      scrollable={true}
    >
      {/* Header Section */}
      <View style={styles.headerSection}>
        <LinearGradient
          colors={['#F59E0B20', theme.colors.background.modal]}
          style={styles.headerGradient}
        >
          <View style={styles.iconContainer}>
            <LinearGradient
              colors={['#F59E0B', '#D97706']}
              style={styles.pauseIcon}
            >
              <Pause size={24} color="#FFFFFF" fill="#FFFFFF" />
            </LinearGradient>
          </View>
          <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
            Take a break and save your progress
          </Text>
        </LinearGradient>
      </View>

      {/* Session Summary */}
      <View style={styles.summarySection}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
          Current Session
        </Text>
        
        <View style={[
          styles.summaryCard,
          {
            backgroundColor: theme.colors.background.tertiary,
            borderColor: theme.colors.ui.border,
          }
        ]}>
          {/* Time Display */}
          <View style={styles.timeDisplay}>
            <Clock size={20} color={theme.colors.accent.primary} />
            <Text style={[styles.timeText, { color: theme.colors.text.primary }]}>
              {formatTime(currentTime)}
            </Text>
          </View>

          {/* Task Info */}
          <View style={styles.taskInfo}>
            <View style={styles.taskHeader}>
                <MaterialIcons
                  name={getTaskTypeIcon(session.taskType) as any}
                  size={20}
                  color={getTaskTypeColor(session.taskType)}
                />
              <Text style={[styles.taskType, { color: theme.colors.text.secondary }]}>
                {session.taskType || 'General Study'}
              </Text>
            </View>
            
            {session.taskName && (
              <Text style={[styles.taskName, { color: theme.colors.text.primary }]}>
                {session.taskName}
              </Text>
            )}
            
            {session.subject && (
              <View style={styles.subjectContainer}>
                <BookOpen size={16} color={theme.colors.text.tertiary} />
                <Text style={[styles.subjectText, { color: theme.colors.text.secondary }]}>
                  {session.subject}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>

      {/* Notes Section */}
      <View style={styles.notesSection}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
          Session Notes (Optional)
        </Text>
        <TextInput
          style={[
            styles.notesInput,
            {
              backgroundColor: theme.colors.background.tertiary,
              borderColor: theme.colors.ui.border,
              color: theme.colors.text.primary,
            }
          ]}
          placeholder="Add any notes about your session..."
          placeholderTextColor={theme.colors.text.tertiary}
          value={notes}
          onChangeText={setNotes}
          multiline
          numberOfLines={3}
          textAlignVertical="top"
          maxLength={300}
        />
        <Text style={[styles.charCount, { color: theme.colors.text.tertiary }]}>
          {notes.length}/300
        </Text>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionSection}>
        <Button
          title="Save & Pause"
          onPress={handlePause}
          variant="primary"
          size="lg"
          style={[
            styles.pauseButton,
            { backgroundColor: '#F59E0B' }
          ]}
          textStyle={{ color: '#FFFFFF', fontWeight: '600' }}
          icon={<Save size={20} color="#FFFFFF" />}
          iconPosition="left"
          loading={isLoading}
          disabled={isLoading}
        />
        
        <Button
          title="Continue Session"
          onPress={handleContinue}
          variant="outline"
          size="lg"
          style={[
            styles.continueButton,
            {
              borderColor: theme.colors.accent.primary,
              backgroundColor: 'transparent',
            }
          ]}
          textStyle={{ color: theme.colors.accent.primary }}
        />
      </View>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  // Removed container style as it is no longer needed
  headerSection: {
    marginBottom: 24,
  },
  headerGradient: {
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 12,
  },
  pauseIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  summarySection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  summaryCard: {
    borderWidth: 1,
    borderRadius: 16,
    padding: 20,
  },
  timeDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  timeText: {
    fontSize: 24,
    fontWeight: '700',
    marginLeft: 8,
  },
  taskInfo: {
    gap: 8,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taskType: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  taskName: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 22,
  },
  subjectContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subjectText: {
    fontSize: 14,
    marginLeft: 6,
  },
  notesSection: {
    marginBottom: 24,
  },
  notesInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 80,
    lineHeight: 22,
  },
  charCount: {
    fontSize: 12,
    textAlign: 'right',
    marginTop: 4,
  },
  actionSection: {
    gap: 12,
  },
  pauseButton: {
    borderRadius: 16,
    paddingVertical: 16,
  },
  continueButton: {
    borderRadius: 16,
    paddingVertical: 16,
    borderWidth: 2,
  },
});
