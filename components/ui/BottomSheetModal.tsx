import React, { useEffect } from 'react';
import {
  Modal as RNModal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  ViewStyle,
  StatusBar,
  Platform,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { X } from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface BottomSheetModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  height?: number | 'auto';
  showCloseButton?: boolean;
  closeOnBackdrop?: boolean;
  style?: ViewStyle;
  scrollable?: boolean;
  showHandle?: boolean;
}

export const BottomSheetModal: React.FC<BottomSheetModalProps> = ({
  visible,
  onClose,
  title,
  children,
  height = 'auto',
  showCloseButton = true,
  closeOnBackdrop = true,
  style,
  scrollable = true,
  showHandle = true,
}) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  
  // Animation values
  const translateY = useSharedValue(screenHeight);
  const opacity = useSharedValue(0);
  
  // Calculate modal height
  const modalHeight = height === 'auto' ? screenHeight * 0.6 : height;
  
  useEffect(() => {
    if (visible) {
      opacity.value = withTiming(1, { duration: theme.transitions.normal });
      translateY.value = withSpring(0, {
        damping: 20,
        stiffness: 300,
        mass: 0.8,
      });
    } else {
      opacity.value = withTiming(0, { duration: theme.transitions.fast });
      translateY.value = withTiming(modalHeight, { duration: theme.transitions.normal });
    }
  }, [visible, modalHeight, theme.transitions]);

  const animatedOverlayStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const animatedModalStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  const animatedBackdropStyle = useAnimatedStyle(() => {
    const backdropOpacity = interpolate(
      translateY.value,
      [0, modalHeight],
      [0.6, 0],
      Extrapolate.CLAMP
    );
    return {
      opacity: backdropOpacity,
    };
  });

  const handleBackdropPress = () => {
    if (closeOnBackdrop) {
      runOnJS(onClose)();
    }
  };

  const handleClose = () => {
    runOnJS(onClose)();
  };

  if (!visible) {
    return null;
  }

  return (
    <RNModal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <StatusBar backgroundColor="transparent" barStyle="light-content" />
      
      {/* Overlay */}
      <Animated.View style={[styles.overlay, animatedOverlayStyle]}>
        <Animated.View style={[styles.backdrop, animatedBackdropStyle]} />
        
        {/* Backdrop touchable */}
        <TouchableOpacity
          style={styles.backdropTouchable}
          activeOpacity={1}
          onPress={handleBackdropPress}
        />
        
        {/* Bottom Sheet */}
        <Animated.View
          style={[
            styles.bottomSheet,
            {
              height: modalHeight,
              backgroundColor: theme.colors.background.modal,
              borderTopColor: theme.colors.ui.border,
            },
            animatedModalStyle,
            style,
          ]}
        >
          <TouchableOpacity activeOpacity={1} onPress={() => {}}>
            <View style={styles.sheetContent}>
              {/* Handle */}
              {showHandle && (
                <View style={styles.handleContainer}>
                  <View
                    style={[
                      styles.handle,
                      { backgroundColor: theme.colors.text.tertiary }
                    ]}
                  />
                </View>
              )}
              
              {/* Header */}
              {(title || showCloseButton) && (
                <View
                  style={[
                    styles.header,
                    {
                      borderBottomColor: theme.colors.ui.border,
                      paddingTop: showHandle ? theme.spacing.sm : theme.spacing.md,
                    }
                  ]}
                >
                  {title && (
                    <Text style={[styles.title, { color: theme.colors.text.primary }]}>
                      {title}
                    </Text>
                  )}
                  {showCloseButton && (
                    <TouchableOpacity
                      style={[
                        styles.closeButton,
                        { backgroundColor: theme.colors.background.tertiary }
                      ]}
                      onPress={handleClose}
                      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                    >
                      <X size={20} color={theme.colors.text.secondary} />
                    </TouchableOpacity>
                  )}
                </View>
              )}
              
              {/* Content */}
              <View style={[styles.content, { paddingBottom: insets.bottom }]}>
                {scrollable ? (
                  <ScrollView
                    style={styles.scrollView}
                    contentContainerStyle={styles.scrollContent}
                    showsVerticalScrollIndicator={false}
                    bounces={false}
                  >
                    {children}
                  </ScrollView>
                ) : (
                  children
                )}
              </View>
            </View>
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>
    </RNModal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000000',
  },
  backdropTouchable: {
    ...StyleSheet.absoluteFillObject,
  },
  bottomSheet: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    borderTopWidth: 1,
    overflow: 'hidden',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 16,
  },
  sheetContent: {
    flex: 1,
  },
  handleContainer: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  handle: {
    width: 40,
    height: 4,
    borderRadius: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    flex: 1,
    lineHeight: 28,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 24,
    paddingTop: 16,
  },
});
